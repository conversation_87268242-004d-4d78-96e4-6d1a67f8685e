<?php

namespace Database\Seeders;

use App\Models\ProductType;
use App\Models\ProductTypeOption;
use App\Models\ProductTypeOptionValue;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $productTypesData = [
            [
                'name' => 'T-Shirt',
                'description' => 'Cotton t-shirts for custom printing',
                'is_active' => true,
                'attributes' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'options' => [
                            ['value' => 'XS', 'sort_order' => 1],
                            ['value' => 'S', 'sort_order' => 2],
                            ['value' => 'M', 'sort_order' => 3],
                            ['value' => 'L', 'sort_order' => 4],
                            ['value' => 'XL', 'sort_order' => 5],
                            ['value' => 'XXL', 'sort_order' => 6],
                        ]
                    ],
                    [
                        'name' => 'Color',
                        'sort_order' => 2,
                        'options' => [
                            ['value' => 'Black', 'sort_order' => 1],
                            ['value' => 'White', 'sort_order' => 2],
                            ['value' => 'Navy', 'sort_order' => 3],
                            ['value' => 'Gray', 'sort_order' => 4],
                            ['value' => 'Red', 'sort_order' => 5],
                        ]
                    ],
                    [
                        'name' => 'Material',
                        'sort_order' => 3,
                        'options' => []
                    ],
                ]
            ],
            [
                'name' => 'Mug',
                'description' => 'Ceramic mugs for custom printing',
                'is_active' => true,
                'attributes' => [
                    [
                        'name' => 'Size',
                        'sort_order' => 1,
                        'options' => [
                            ['value' => '11oz', 'sort_order' => 1],
                            ['value' => '15oz', 'sort_order' => 2],
                            ['value' => '20oz', 'sort_order' => 3],
                        ]
                    ],
                    [
                        'name' => 'Color',
                        'sort_order' => 2,
                        'options' => [
                            ['value' => 'White', 'sort_order' => 1],
                            ['value' => 'Black', 'sort_order' => 2],
                            ['value' => 'Blue', 'sort_order' => 3],
                        ]
                    ],
                ]
            ],
        ];

        foreach ($productTypesData as $productTypeData) {
            $attributes = $productTypeData['attributes'];
            unset($productTypeData['attributes']);

            $productType = ProductType::updateOrCreate(
                ['name' => $productTypeData['name']],
                $productTypeData
            );

            // Clear existing options to avoid duplicates
            $productType->options()->delete();

            foreach ($attributes as $attributeData) {
                $values = $attributeData['options'];
                unset($attributeData['options']);
                $attributeData['product_type_id'] = $productType->id;

                $option = ProductTypeOption::create($attributeData);

                foreach ($values as $valueData) {
                    $valueData['product_type_option_id'] = $option->id;
                    ProductTypeOptionValue::create($valueData);
                }
            }
        }
    }
}
