<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductType>
 */
class ProductTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $productTypes = [
            'T-Shirt' => [
                'description' => 'Cotton t-shirts for printing',
                'attributes' => [
                    ['name' => 'Size', 'type' => 'select', 'options' => ['XS', 'S', 'M', 'L', 'XL', 'XXL']],
                    ['name' => 'Color', 'type' => 'select', 'options' => ['Black', 'White', 'Navy', 'Gray']],
                    ['name' => 'Material', 'type' => 'text'],
                ]
            ],
            'Mug' => [
                'description' => 'Ceramic mugs for printing',
                'attributes' => [
                    ['name' => 'Size', 'type' => 'select', 'options' => ['11oz', '15oz']],
                    ['name' => 'Color', 'type' => 'select', 'options' => ['White', 'Black']],
                    ['name' => 'Handle Color', 'type' => 'text'],
                ]
            ],
            'Phone Case' => [
                'description' => 'Phone cases for various models',
                'attributes' => [
                    ['name' => 'Model', 'type' => 'text'],
                    ['name' => 'Material', 'type' => 'select', 'options' => ['Plastic', 'Silicone', 'Leather']],
                    ['name' => 'Color', 'type' => 'text'],
                ]
            ],
        ];

        $type = $this->faker->randomElement(array_keys($productTypes));
        $typeData = $productTypes[$type];

        return [
            'name' => $type,
            'description' => $typeData['description'],
            'default_attributes' => $typeData['attributes'],
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }
}
