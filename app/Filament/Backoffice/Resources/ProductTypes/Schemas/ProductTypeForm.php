<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\Schemas;

use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductTypeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Basic Information')
                    ->schema([
                        TextInput::make('name')
                            ->label('Product Type Name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->placeholder('e.g., T-Shirt, Mug, Phone Case'),

                        Textarea::make('description')
                            ->label('Description')
                            ->rows(3)
                            ->placeholder('Describe this product type...'),

                        Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Inactive product types will not be available for new designs'),
                    ])
                    ->columns(1),

                Section::make('Options')
                    ->description('Define the common options that products of this type typically have (e.g., Size, Color, Material)')
                    ->schema([
                        Repeater::make('attributes')
                            ->label('Options')
                            ->schema([
                                TextInput::make('name')
                                    ->label('Option Name')
                                    ->required()
                                    ->placeholder('e.g., Size, Color, Material'),

                                TagsInput::make('option_values')
                                    ->label('Values')
                                    ->placeholder('Type and press Enter to add values (e.g., S, M, L)')
                                    ->helperText('Add values for dropdown selection. Leave empty for text input.')
                                    ->dehydrated(false),
                            ])
                            ->columns(2)
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['name'] ?? null)
                            ->addActionLabel('Add Option')
                            ->defaultItems(0)
                            ->reorderable(),
                    ])
                    ->collapsible(),
            ]);
    }
}
