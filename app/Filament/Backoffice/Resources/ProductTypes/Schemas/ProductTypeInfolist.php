<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;

class ProductTypeInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Product Type Information')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Name')
                            ->weight('medium'),

                        TextEntry::make('description')
                            ->label('Description')
                            ->placeholder('No description provided'),

                        IconEntry::make('is_active')
                            ->label('Status')
                            ->boolean()
                            ->trueIcon(Heroicon::OutlinedCheckCircle)
                            ->falseIcon(Heroicon::OutlinedXCircle)
                            ->trueColor('success')
                            ->falseColor('danger'),

                        TextEntry::make('created_at')
                            ->label('Created At')
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label('Updated At')
                            ->dateTime(),
                    ])
                    ->columns(2),

                Section::make('Options')
                    ->schema([
                        RepeatableEntry::make('options')
                            ->label('')
                            ->schema([
                                TextEntry::make('name')
                                    ->label('Option Name')
                                    ->weight('medium'),

                                TextEntry::make('values')
                                    ->label('Values')
                                    ->formatStateUsing(function ($record) {
                                        if (!$record || !$record->values || $record->values->count() === 0) {
                                            return 'Text input (no values)';
                                        }
                                        return $record->values->pluck('value')->implode(', ');
                                    }),
                            ])
                            ->columns(2)
                            ->placeholder('No options defined'),
                    ])
                    ->collapsible(),
            ]);
    }
}
