<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\RelationManagers;

use App\Actions\CreateProductTypeOption;
use App\Actions\UpdateProductTypeOption;
use App\Models\ProductTypeOption;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class OptionsRelationManager extends RelationManager
{
    protected static string $relationship = 'options';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->minimal()
            ->recordTitleAttribute('name')
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('values.value')->badge(),
                TextColumn::make('sort_order'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->schema([
                        TextInput::make('name')
                            ->required(),
                        TagsInput::make('values')
                            ->required()
                            ->hint("Press enter to add a new value")
                            ->reorderable()
                    ])
                    ->createAnother(false)
                    ->action(function (array $data) {
                        CreateProductTypeOption::make()->handle(
                            $this->getOwnerRecord(),
                            $data['name'],
                            $data['values'],
                        );
                    }),
            ])
            ->recordActions([
                EditAction::make()
                    ->schema([
                        TextInput::make('name')
                            ->required(),
                        TagsInput::make('values')
                            ->hint("Press enter to add a new value")
                            ->helperText('You can drag values to reorder them')
                            ->reorderable()
                    ])
                    ->fillForm(function (ProductTypeOption $record): array {
                        return [
                            'name'   => $record->name,
                            'values' => $record->values->pluck('value')->toArray(),
                        ];
                    })
                    ->action(function (ProductTypeOption $record, array $data): void {
                        UpdateProductTypeOption::make()->handle(
                            $record,
                            $data['name'],
                            $data['values'] ?? [],
                        );
                    }),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
