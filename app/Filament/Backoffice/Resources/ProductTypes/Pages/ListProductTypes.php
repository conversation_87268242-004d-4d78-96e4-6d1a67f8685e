<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\Pages;

use App\Filament\Backoffice\Resources\ProductTypes\ProductTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProductTypes extends ListRecords
{
    protected static string $resource = ProductTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
