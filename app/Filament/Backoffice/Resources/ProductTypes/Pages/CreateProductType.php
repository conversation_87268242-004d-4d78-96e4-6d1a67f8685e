<?php

namespace App\Filament\Backoffice\Resources\ProductTypes\Pages;

use App\Filament\Backoffice\Resources\ProductTypes\ProductTypeResource;
use Filament\Resources\Pages\CreateRecord;

class CreateProductType extends CreateRecord
{
    protected static string $resource = ProductTypeResource::class;

    protected function afterCreate(): void
    {
        $attributesData = $this->data['attributes'] ?? [];
        $this->record->saveAttributesWithOptions($attributesData);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Remove attributes from main data since we handle them separately
        unset($data['attributes']);
        return $data;
    }
}
