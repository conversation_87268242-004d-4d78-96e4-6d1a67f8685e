<?php

namespace App\Actions;

use App\Models\ProductTypeOption;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateProductTypeOption
{
    use AsAction;

    public function handle(ProductTypeOption $option, string $optionName, array $values): ProductTypeOption
    {
        // Update the option name
        $option->update([
            'name' => $optionName,
        ]);

        // Clear existing values and create new ones
        $option->values()->delete();

        // Create the new option values if provided
        if (!empty($values)) {
            foreach ($values as $index => $value) {
                $option->values()->create([
                    'value' => $value,
                    'sort_order' => $index + 1,
                ]);
            }
        }

        return $option->fresh(['values']);
    }
}
