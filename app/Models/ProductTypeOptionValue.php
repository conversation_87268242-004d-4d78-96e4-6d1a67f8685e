<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductTypeOptionValue extends Model
{
    protected $fillable = [
        'product_type_option_id',
        'value',
        'sort_order',
    ];

    protected function casts(): array
    {
        return [
            'sort_order' => 'integer',
        ];
    }

    public function option(): BelongsTo
    {
        return $this->belongsTo(ProductTypeOption::class, 'product_type_option_id');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }


}
