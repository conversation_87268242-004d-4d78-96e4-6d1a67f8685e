<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductTypeOption extends Model
{
    /** @use HasFactory<\Database\Factories\ProductTypeOptionFactory> */
    use HasFactory;

    protected function casts(): array
    {
        return [
            'sort_order' => 'integer',
        ];
    }

    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    public function values(): HasMany
    {
        return $this->hasMany(ProductTypeOptionValue::class, 'product_type_option_id')->orderBy('sort_order');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    public function getValuesListAttribute(): array
    {
        return $this->values->pluck('value')->toArray();
    }
}
