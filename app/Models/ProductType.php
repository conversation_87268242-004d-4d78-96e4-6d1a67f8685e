<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductType extends Model
{
    /** @use HasFactory<\Database\Factories\ProductTypeFactory> */
    use HasFactory;

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    public function options(): HasMany
    {
        return $this->hasMany(ProductTypeOption::class)->orderBy('sort_order');
    }

    public function designs(): HasMany
    {
        return $this->hasMany(Design::class);
    }

    public function productVariations(): HasMany
    {
        return $this->hasMany(ProductVariation::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function getOptionsListAttribute(): array
    {
        return $this->options->pluck('name')->toArray();
    }

    public function saveAttributesWithOptions(array $attributesData): void
    {
        // Clear existing options
        $this->options()->delete();

        foreach ($attributesData as $index => $attributeData) {
            $optionValues = $attributeData['option_values'] ?? [];
            unset($attributeData['option_values']);

            $attributeData['product_type_id'] = $this->id;
            $attributeData['sort_order'] = $index + 1;

            $option = $this->options()->create($attributeData);

            // Create values if provided
            if (!empty($optionValues)) {
                foreach ($optionValues as $optionIndex => $value) {
                    $option->values()->create([
                        'value' => $value,
                        'sort_order' => $optionIndex + 1,
                    ]);
                }
            }
        }
    }
}
