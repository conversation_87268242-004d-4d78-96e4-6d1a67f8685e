<?php

use App\Models\ProductType;

test('can create product type', function () {
    $productType = ProductType::create([
        'name' => 'Test Product Type',
        'description' => 'A test product type',
        'is_active' => true,
    ]);

    expect($productType->name)->toBe('Test Product Type');
    expect($productType->is_active)->toBeTrue();
    expect($productType->options)->toBeInstanceOf(\Illuminate\Database\Eloquent\Collection::class);
});

test('product type name must be unique', function () {
    ProductType::create([
        'name' => 'Unique Product Type',
        'description' => 'First product type',
        'is_active' => true,
    ]);

    expect(fn () => ProductType::create([
        'name' => 'Unique Product Type',
        'description' => 'Second product type',
        'is_active' => true,
    ]))->toThrow(\Illuminate\Database\QueryException::class);
});

test('active scope works', function () {
    ProductType::create(['name' => 'Active Type', 'is_active' => true]);
    ProductType::create(['name' => 'Inactive Type', 'is_active' => false]);

    $activeTypes = ProductType::active()->get();

    expect($activeTypes)->toHaveCount(1);
    expect($activeTypes->first()->name)->toBe('Active Type');
});

test('can create product type with options', function () {
    $productType = ProductType::create([
        'name' => 'Test Product Type with Options',
        'description' => 'A test product type',
        'is_active' => true,
    ]);

    $option = $productType->options()->create([
        'name' => 'Size',
        'sort_order' => 1,
    ]);

    $option->values()->createMany([
        ['value' => 'S', 'sort_order' => 1],
        ['value' => 'M', 'sort_order' => 2],
        ['value' => 'L', 'sort_order' => 3],
    ]);

    expect($productType->options)->toHaveCount(1);
    expect($productType->options->first()->name)->toBe('Size');
    expect($productType->options->first()->values)->toHaveCount(3);
    expect($productType->options->first()->values->first()->value)->toBe('S');
});
